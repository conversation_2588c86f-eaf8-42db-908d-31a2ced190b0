
-----------------------------
2025-07-10 14:00:02: Reminder triggered for invoice no:6215
------------------------------
2025-07-10 14:00:02:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"<PERSON>",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=8fea4cae8f68757492d5211e7dcde36f",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-1a52e63fff8e429eaa0b5a344d19066c24be69798cfc4fdf842cec3ef61eb8274bb447abd7394ea8b53b623504c40b34?locale=en_US&cta=v3invoicelink",
							"business_name":"GAINES NATIONAL CARRIERS, LLC - CCB",
							"invoice_id":"7675",
							"invoice_number":"7675",
							"invoice_date":"01/02/2024",
							"invoice_due_date":"01/12/2024",
							"overdue_days":"545",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"530.50",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:00:03:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"MTMxMTY3YjMtNWQ2OC0xMWYwLWJmMDEtMjZjYzRiZGVlNDViLWIwYjNjYjNiMw","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:00:04:Reminder email Note added to project and lead sent data:{"lead_id":"453","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7675","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:00:04:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"453","note_id":215797}-----------------------------
2025-07-10 14:00:04: Reminder triggered for invoice no:6174
------------------------------
2025-07-10 14:00:04:Reminder email api sent data :{
					"template_id": "d-5b156e05b1f84be79ead04260c61bebf",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Kelly Simpson",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=1e149e1953e943ccc929d08b4e88e18e",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-d80c0cde3f974a32adb2d40067e87ed5bfa27470e6ff4a5d94de6bf878bd6108e44f9bdca8eb4b75ac254964c53da1d6?locale=en_US&cta=v3invoicelink",
							"business_name":"Hair Corp Smyrna Inc",
							"invoice_id":"7634",
							"invoice_number":"7634",
							"invoice_date":"07/02/2025",
							"invoice_due_date":"07/02/2025",
							"overdue_days":"8",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"1263.93",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:00:05:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"MTQ5ZjVlODUtNWQ2OC0xMWYwLTkyODctZmE2YzljM2EzZmQ2LWU1ZjcxY2NmNw","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:00:06:Reminder email Note added to project and lead sent data:{"lead_id":"5930","product_id":"935","note":"An invoice reminder was sent using the Invoice: Initial Friendly Reminder template for Invoice Id #7634","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:00:06:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"5930","note_id":215798}------------------ AT
2025-07-10 14:00:07: Reminder is terminated the loop for Test lead invoice Lead ids 9362and invoice id is6093-----------------------------
2025-07-10 14:00:07: Reminder triggered for invoice no:6061
------------------------------
2025-07-10 14:00:07:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Travis Sparks",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=b992c11b61be98efb3b4774e7d267f8a",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-fbebbbb3915c4122a2ebfac6c48f775cff2ca3813edb4fc882104ac3f1be70926013fc4d79d5476cad3e468e943c0fb1?locale=en_US&cta=v3invoicelink",
							"business_name":"Rams Pharmacy",
							"invoice_id":"7521",
							"invoice_number":"7521",
							"invoice_date":"11/11/2024",
							"invoice_due_date":"11/11/2024",
							"overdue_days":"241",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"1906.96",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:00:08:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"MTY2NjQ4MjMtNWQ2OC0xMWYwLTgwZDctYmE0YjYxNGJhM2ViLTcxMWU0MTIzOA","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:00:09:Reminder email Note added to project and lead sent data:{"lead_id":"6384","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7521","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:00:09:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"6384","note_id":215799}-----------------------------
2025-07-10 14:00:10: Reminder triggered for invoice no:6012
------------------------------
2025-07-10 14:00:10:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>",
						"bcc":"<EMAIL>",
						"data": {
							"customer_name":"Chris Hogan",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=02e0bab4d4aac6b752794b1502705558",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-0f1ea4f3688a422f8907c80f9fa13e1e333971bb5a3a499e8432506b31f9e0e4dafc24d934cd4ae985e47d6d4517c4c3?locale=en_US&cta=v3invoicelink",
							"business_name":"Paddle Board Outfitters",
							"invoice_id":"7472",
							"invoice_number":"7472",
							"invoice_date":"06/09/2025",
							"invoice_due_date":"06/09/2025",
							"overdue_days":"31",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"1043.93",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:00:11:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"MTdiYzIyYjUtNWQ2OC0xMWYwLWIyNTMtNGEzNzY1N2U4YWNkLTM5MzMxNWUzYQ","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:00:11:Reminder email Note added to project and lead sent data:{"lead_id":"2123","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7472","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:00:11:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"2123","note_id":215801}-----------------------------
2025-07-10 14:00:12: Reminder triggered for invoice no:6010
------------------------------
2025-07-10 14:00:12:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>",
						"bcc":"<EMAIL>",
						"data": {
							"customer_name":"Shadi Sayes",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=f000628db01a4eb760c832436a62907c",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-aea6690c85744306b6779c9ad6de937811e0f91356164cb6a775dd04e55581e5095c0b6803fc462bb7579b2645151343?locale=en_US&cta=v3invoicelink",
							"business_name":"IMAGE ONE ENTERPRISES LLC",
							"invoice_id":"7470",
							"invoice_number":"7470",
							"invoice_date":"06/09/2025",
							"invoice_due_date":"06/09/2025",
							"overdue_days":"31",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"8449.69",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:00:12:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"MThlMWJmZjMtNWQ2OC0xMWYwLWEzNDEtZGE5M2I3YjU2YWYwLTgzMjcwMGFiOQ","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:00:13:Reminder email Note added to project and lead sent data:{"lead_id":"1364","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7470","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:00:13:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"1364","note_id":215802}-----------------------------
2025-07-10 14:00:13: Reminder triggered for invoice no:6008
------------------------------
2025-07-10 14:00:13:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"<EMAIL>",
						"data": {
							"customer_name":"BORIS FURMAN",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=5c970b28d7ba7a4a6115800d9b76f56f",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-5fd8119c58f946c1b784285022a98b5042e48ae257614149aa3dca269838582aac23c593006340388b87604105e90c32?locale=en_US&cta=v3invoicelink",
							"business_name":"BF ALLIANCE LLC",
							"invoice_id":"7468",
							"invoice_number":"7468",
							"invoice_date":"06/09/2025",
							"invoice_due_date":"06/09/2025",
							"overdue_days":"31",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"1443.65",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:00:14:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"MWEwNDc1NzgtNWQ2OC0xMWYwLTk5MmUtNmFlNGI1NzMxODI2LTQ1Y2M1ZTQxZQ","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:00:15:Reminder email Note added to project and lead sent data:{"lead_id":"6784","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7468","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:00:15:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"6784","note_id":215804}-----------------------------
2025-07-10 14:00:15: Reminder triggered for invoice no:6006
------------------------------
2025-07-10 14:00:15:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"<EMAIL>",
						"data": {
							"customer_name":"Erin Watson",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=4f99986dd937c4b353b6942990cd5f26",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-6e44a42375a94939bf6c86b9d9aa25269efc65babbc84888b2f91e03bafb17157bc8a20cfac440fb879461fc27bc2ca3?locale=en_US&cta=v3invoicelink",
							"business_name":"Tom's Bulldog Automotive Inc",
							"invoice_id":"7466",
							"invoice_number":"7466",
							"invoice_date":"06/09/2025",
							"invoice_due_date":"06/09/2025",
							"overdue_days":"31",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"4075.19",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:00:16:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"MWIyYTMzMjgtNWQ2OC0xMWYwLWJkYTYtYmEwYjZkZTQ5MDQ2LTI2OWM0YTMxYg","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:00:17:Reminder email Note added to project and lead sent data:{"lead_id":"2193","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7466","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:00:17:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"2193","note_id":215805}-----------------------------
2025-07-10 14:00:17: Reminder triggered for invoice no:6005
------------------------------
2025-07-10 14:00:17:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"<EMAIL>",
						"data": {
							"customer_name":"Christin Yam Liu",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=9aff38eacf9d2afc6ea1757247290572",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-50fb274e07ce408ba419eb0b1a0b5d49a45cfe1b941547efba32944ea442a5ad903de06769874954954ad9a1e0076ff1?locale=en_US&cta=v3invoicelink",
							"business_name":"Poise Gracefulness Inc",
							"invoice_id":"7465",
							"invoice_number":"7465",
							"invoice_date":"06/09/2025",
							"invoice_due_date":"06/09/2025",
							"overdue_days":"31",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"376.69",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:00:18:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"MWM0YzY5NGYtNWQ2OC0xMWYwLTk1ODEtYmE4Y2JlZDQ4ZTc2LTY2Yzk0MjhjNA","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:00:19:Reminder email Note added to project and lead sent data:{"lead_id":"2174","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7465","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:00:19:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"2174","note_id":215806}-----------------------------
2025-07-10 14:00:19: Reminder triggered for invoice no:6001
------------------------------
2025-07-10 14:00:19:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"<EMAIL>",
						"data": {
							"customer_name":"James M Sims",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=9c9d085a46e6ecc6b78de4a7198797e1",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-ef6d697ffcca407f95ad5ccbdf7f0dcdb40aaed35f214163ad19f232bd18cdd43dfad5ca72e9487a8fd2a83cc076ad16?locale=en_US&cta=v3invoicelink",
							"business_name":"Sims Paving Inc.",
							"invoice_id":"7461",
							"invoice_number":"7461",
							"invoice_date":"06/09/2025",
							"invoice_due_date":"06/09/2025",
							"overdue_days":"31",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"1190.80",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:00:20:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"MWQ2ZjBjZGMtNWQ2OC0xMWYwLThhMjItNTI5Njc5MTZiNmMzLTJmZDVjY2Q5OQ","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:00:21:Reminder email Note added to project and lead sent data:{"lead_id":"6350","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7461","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:00:21:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"6350","note_id":215808}-----------------------------
2025-07-10 14:00:21: Reminder triggered for invoice no:6000
------------------------------
2025-07-10 14:00:21:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Scott Walker",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=1c7bc457d5eb3d96f1e2cea6511b18b8",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-a744c23b1c9f4046a8955fffe49d35cfd9283ec941384464a679ff7ea48ecb9d2b3d20d8424d4dc3a2c7c214725b8cc5?locale=en_US&cta=v3invoicelink",
							"business_name":"Rutherford Group Inc",
							"invoice_id":"7460",
							"invoice_number":"7460",
							"invoice_date":"06/09/2025",
							"invoice_due_date":"06/09/2025",
							"overdue_days":"31",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"5562.04",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:00:22:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"MWU5M2FkMjgtNWQ2OC0xMWYwLWJjZmQtYmExMTUyMmFlZGMwLTg3ODllMWQxYw","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:00:23:Reminder email Note added to project and lead sent data:{"lead_id":"7295","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7460","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:00:23:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"7295","note_id":215809}-----------------------------
2025-07-10 14:00:23: Reminder triggered for invoice no:5997
------------------------------
2025-07-10 14:00:23:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"<EMAIL>",
						"data": {
							"customer_name":"Charles E",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=749c002b080d4203b904e3653c0d032c",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-c5797bce02134c80b3e979c037ae2c29bf61d11d2708459a823408b614089bbe9c28dc2d0a05452ba5845931e795869b?locale=en_US&cta=v3invoicelink",
							"business_name":"Florida State Roofing And Construction Inc",
							"invoice_id":"7457",
							"invoice_number":"7457",
							"invoice_date":"06/09/2025",
							"invoice_due_date":"06/09/2025",
							"overdue_days":"31",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"13679.97",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:00:24:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"MWZiNTk4MjYtNWQ2OC0xMWYwLWE5ZDgtZWViZmExMzdkNWNhLThlN2Q0NTJjMQ","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:00:25:Reminder email Note added to project and lead sent data:{"lead_id":"1901","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7457","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:00:25:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"1901","note_id":215810}-----------------------------
2025-07-10 14:00:25: Reminder triggered for invoice no:5974
------------------------------
2025-07-10 14:00:25:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>",
						"bcc":"<EMAIL>",
						"data": {
							"customer_name":"Shobhna Bhatia",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=262a6913c149dcd7ece3b2df7a7d0b66",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-29556363c3bc42ffb9097bc2e519567f60a8d300cc494fd5ba24208844e87c1038aa801d5076406391ab8dd17078f757?locale=en_US&cta=v3invoicelink",
							"business_name":"Health And Beauty Marketing Group I",
							"invoice_id":"7434",
							"invoice_number":"7434",
							"invoice_date":"05/26/2025",
							"invoice_due_date":"05/26/2025",
							"overdue_days":"45",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"4609.14",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:00:26:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"MjBlNTBmMDctNWQ2OC0xMWYwLTgwODMtZTJiNGRmYjA1YTk3LWJmNzRjZTkwZg","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:00:27:Reminder email Note added to project and lead sent data:{"lead_id":"2222","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7434","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:00:27:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"2222","note_id":215812}-----------------------------
2025-07-10 14:00:27: Reminder triggered for invoice no:5968
------------------------------
2025-07-10 14:00:27:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Brandon Santander",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=0d5646942d9ceeb0f725a6031a6d118a",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-2b88965e7e864146bf91def8621dda2a052cbff3706443af86194382f4404a4ae28659a8bee743f58eef4f3e6026e098?locale=en_US&cta=v3invoicelink",
							"business_name":"Veterans Pressure Washing Inc",
							"invoice_id":"7428",
							"invoice_number":"7428",
							"invoice_date":"05/26/2025",
							"invoice_due_date":"05/26/2025",
							"overdue_days":"45",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"4446.21",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:00:28:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"MjIwNTU4YTgtNWQ2OC0xMWYwLWEzNGItMWE0MDg3YzE3OGI0LTY4MmMzYjgxMA","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:00:29:Reminder email Note added to project and lead sent data:{"lead_id":"5705","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7428","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:00:29:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"5705","note_id":215813}-----------------------------
2025-07-10 14:00:29: Reminder triggered for invoice no:5967
------------------------------
2025-07-10 14:00:29:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Theodora Hough",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=7eaaee7563c52228273ef084b58e066d",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-ebcc94fceb4645af9b1a92f8318ce177cc7a383bb3a549c0af082007c2a3e71fe5a8652af98d418ca01ad12c8bff4e8f?locale=en_US&cta=v3invoicelink",
							"business_name":"The Education Station Academy Inc",
							"invoice_id":"7427",
							"invoice_number":"7427",
							"invoice_date":"05/26/2025",
							"invoice_due_date":"05/26/2025",
							"overdue_days":"45",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"5955.80",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:00:30:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"MjMyNWI5NjMtNWQ2OC0xMWYwLTk3MzAtZGE5M2I3YjU2YWYwLWZjMjk1ZjE5Mg","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:00:31:Reminder email Note added to project and lead sent data:{"lead_id":"8067","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7427","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:00:31:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"8067","note_id":215814}-----------------------------
2025-07-10 14:00:31: Reminder triggered for invoice no:5964
------------------------------
2025-07-10 14:00:31:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"HIMANSHU PATEL",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=8495196753c93487bce0a995e7bb8340",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-f2ba25ccc9614ad9a16f0555db4e559040848cbdfbf948e9962ad577240af26d684d318a221347c29263d1f42ca3e691?locale=en_US&cta=v3invoicelink",
							"business_name":"SHREEJI 151 COLUMBIA TPKE INC",
							"invoice_id":"7424",
							"invoice_number":"7424",
							"invoice_date":"05/26/2025",
							"invoice_due_date":"05/26/2025",
							"overdue_days":"45",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"6143.77",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:00:32:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"MjQ2MjU1MjYtNWQ2OC0xMWYwLTgwOGEtZTJiNGRmYjA1YTk3LWRlZTRlMGQzYg","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:00:33:Reminder email Note added to project and lead sent data:{"lead_id":"6472","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7424","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:00:33:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"6472","note_id":215816}-----------------------------
2025-07-10 14:00:33: Reminder triggered for invoice no:5963
------------------------------
2025-07-10 14:00:33:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Roy Matlock",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=d379d642861a2149130f2e04ab13be84",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-19a49d82343041fba90902a33de7bbcc8a5e400b6f7944219c51d9b2fbdf4c3063f99ec8391f4b36ba18a48cfc18dfaf?locale=en_US&cta=v3invoicelink",
							"business_name":"SalesTeam Pro LLC",
							"invoice_id":"7423",
							"invoice_number":"7423",
							"invoice_date":"05/26/2025",
							"invoice_due_date":"05/26/2025",
							"overdue_days":"45",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"2573.91",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:00:34:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"MjViYmZiYWItNWQ2OC0xMWYwLWJmZTEtZTJiNGRmYjA1YTk3LWYwODU2ZGYwNg","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:00:35:Reminder email Note added to project and lead sent data:{"lead_id":"8175","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7423","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:00:35:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"8175","note_id":215817}-----------------------------
2025-07-10 14:00:35: Reminder triggered for invoice no:5962
------------------------------
2025-07-10 14:00:35:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Sandra Lisa Ramirez",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=25ff0ce418be1a8e9e7584c63055b05e",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-b9e130f4d5554535a9a3fefc4db7b69dd67c9d055e7040ce8fa614a2363f9acb893eb2d0651e41d78013abc2563dc637?locale=en_US&cta=v3invoicelink",
							"business_name":"Resource Facility Solutions LLC",
							"invoice_id":"7422",
							"invoice_number":"7422",
							"invoice_date":"05/26/2025",
							"invoice_due_date":"05/26/2025",
							"overdue_days":"45",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"17949.49",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:00:36:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"MjZkOTRlNjYtNWQ2OC0xMWYwLTg0MmUtM2UwMzkxMTFlMDg4LTc2Mzk5ZTc2Yw","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:00:37:Reminder email Note added to project and lead sent data:{"lead_id":"7477","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7422","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:00:37:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"7477","note_id":215818}-----------------------------
2025-07-10 14:00:37: Reminder triggered for invoice no:5961
------------------------------
2025-07-10 14:00:37:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Catherine Klinchuch",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=3c2de74dc9219b3b9c25e681ec3127b5",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-3a9d685a8aa845a79ff2814432c07e0f7cf6659700684256934147312ce871aa141b1a5551a74af98d2620507c8c4a5b?locale=en_US&cta=v3invoicelink",
							"business_name":"Paytient Technologies Inc",
							"invoice_id":"7421",
							"invoice_number":"7421",
							"invoice_date":"05/26/2025",
							"invoice_due_date":"05/26/2025",
							"overdue_days":"45",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"2514.44",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:00:38:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"MjdmZjhjYjMtNWQ2OC0xMWYwLWJhOTYtNmFlNGI1NzMxODI2LWQ5YmM3MTI0Yw","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:00:39:Reminder email Note added to project and lead sent data:{"lead_id":"7280","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7421","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:00:39:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"7280","note_id":215819}-----------------------------
2025-07-10 14:00:39: Reminder triggered for invoice no:5957
------------------------------
2025-07-10 14:00:39:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Sharon Strange",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=b9d2a5ad59b6ffa4102c9908a0c5936d",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-e5064aba757d486c94d1e77e80f90e002c666a56f67941899c70129350ce960d7ead6c8b667c4c989db7f68244d8f5a8?locale=en_US&cta=v3invoicelink",
							"business_name":"M&S PREMIUM SERVICES LLC",
							"invoice_id":"7417",
							"invoice_number":"7417",
							"invoice_date":"05/26/2025",
							"invoice_due_date":"05/26/2025",
							"overdue_days":"45",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"2322.45",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:00:40:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"MjkxZTM1NzUtNWQ2OC0xMWYwLTgyYjMtM2VhMGM4MjhmNDAyLWFmZmMyYmI2ZA","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:00:41:Reminder email Note added to project and lead sent data:{"lead_id":"6630","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7417","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:00:41:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"6630","note_id":215821}-----------------------------
2025-07-10 14:00:41: Reminder triggered for invoice no:5952
------------------------------
2025-07-10 14:00:41:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"<EMAIL>",
						"data": {
							"customer_name":"Saurin Patel",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=995aacee4448fdda1d2b409b5b21681e",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-db080eef260846148ae9f14173dc61a634f36085572248a8b5c16eb218c03d80eefc358cd1774a1fa4da6e34c9891e90?locale=en_US&cta=v3invoicelink",
							"business_name":"CHANDANI CORPORATION",
							"invoice_id":"7412",
							"invoice_number":"7412",
							"invoice_date":"05/26/2025",
							"invoice_due_date":"05/26/2025",
							"overdue_days":"45",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"560.89",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:00:42:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"MmE0YzdhMmItNWQ2OC0xMWYwLWE1ZmQtM2VhMGM4MjhmNDAyLWVkNzJkYjE1NA","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:00:42:Reminder email Note added to project and lead sent data:{"lead_id":"4074","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7412","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:00:42:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"4074","note_id":215822}------------------ AT
2025-07-10 14:00:43: Reminder not send for payment plan lead, terminated the loop for invoice no:5874 and lead id:621-----------------------------
2025-07-10 14:00:43: Reminder triggered for invoice no:5870
------------------------------
2025-07-10 14:00:43:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Christopher Wilson",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=9b5a7492d2b1b14d600dfd502768cd05",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-420a61d0c2b34f278d33e94e79f7616f849d6c5f823c4aba941863c9e6eb7db476b650b792264cd99478ec03ab1b6ca3?locale=en_US&cta=v3invoicelink",
							"business_name":"Weber Motors Fresno Inc",
							"invoice_id":"7330",
							"invoice_number":"7330",
							"invoice_date":"05/12/2025",
							"invoice_due_date":"05/12/2025",
							"overdue_days":"59",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"21440.17",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:00:44:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"MmJhY2RjMTQtNWQ2OC0xMWYwLTgyNmMtOGU0NDViZjg0NzQxLTkxYTkzZDNmYg","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:00:45:Reminder email Note added to project and lead sent data:{"lead_id":"5293","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7330","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:00:45:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"5293","note_id":215823}-----------------------------
2025-07-10 14:00:45: Reminder triggered for invoice no:5855
------------------------------
2025-07-10 14:00:45:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Russell Ouchida",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=923cc0e213ec0ac56cebb75fba32ccdb",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-9aed087a7cd14aab879c4af0935c3e8a4f30d020adb5412cae80c9396da1a03b13a569701a7f46b5addfbc21bc5e46e8?locale=en_US&cta=v3invoicelink",
							"business_name":"Karum Group LLC",
							"invoice_id":"7315",
							"invoice_number":"7315",
							"invoice_date":"05/12/2025",
							"invoice_due_date":"05/12/2025",
							"overdue_days":"59",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"20430.60",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:00:46:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"MmNjYTZmMDAtNWQ2OC0xMWYwLTlhMmEtNmUxNTljNjc4MWQwLTdiMDY3OGM4Ng","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:00:47:Reminder email Note added to project and lead sent data:{"lead_id":"5674","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7315","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:00:47:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"5674","note_id":215825}-----------------------------
2025-07-10 14:00:47: Reminder triggered for invoice no:5849
------------------------------
2025-07-10 14:00:47:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"<EMAIL>",
						"data": {
							"customer_name":"Saurin Patel",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=5b1865a5f20afeb24059d669bfe99f83",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-88c85e6f70814c2f9e8349026069b8f0ec403864d28244dc84ed1c847f71661a6c36cbbca1b1461fa315887a985fb53c?locale=en_US&cta=v3invoicelink",
							"business_name":"HDP CORPORATION",
							"invoice_id":"7309",
							"invoice_number":"7309",
							"invoice_date":"05/12/2025",
							"invoice_due_date":"05/12/2025",
							"overdue_days":"59",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"923.57",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:00:48:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"MmRlYzcwOTktNWQ2OC0xMWYwLWJiYWMtNGEzNzY1N2U4YWNkLTZjYTczYWJkYQ","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:00:48:Reminder email Note added to project and lead sent data:{"lead_id":"4082","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7309","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:00:48:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"4082","note_id":215826}-----------------------------
2025-07-10 14:00:49: Reminder triggered for invoice no:5846
------------------------------
2025-07-10 14:00:49:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"<EMAIL>",
						"data": {
							"customer_name":"Kevin Longhacker",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=8ca8342009cdce2564022007de30dae2",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-3dc6f343848c4c44b4367cdf4299acfa334938aa69e846e19e1fe40570cb117228af84d272414879aaff51a694b87895?locale=en_US&cta=v3invoicelink",
							"business_name":"EVG VETERINARIANS OF NORTH ELM PLLC",
							"invoice_id":"7306",
							"invoice_number":"7306",
							"invoice_date":"05/12/2025",
							"invoice_due_date":"05/12/2025",
							"overdue_days":"59",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"6834.49",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:00:50:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"MmYwYTIzNGYtNWQ2OC0xMWYwLWIzM2ItZmFjZjU5MDAwYzA1LTIyMGExMWVkNQ","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:00:51:Reminder email Note added to project and lead sent data:{"lead_id":"6497","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7306","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:00:51:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"6497","note_id":215827}-----------------------------
2025-07-10 14:00:51: Reminder triggered for invoice no:5831
------------------------------
2025-07-10 14:00:51:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Angela Bowlson",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=d0a5f46eb80ba8a0c4bfdfe166d76557",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-91bdbb79cd3243e691d0e0f7009ea5c1f1e4f452cfa04b4e9c8edb3531f5979c500b007ab4a840fbb864b2ad11698d6d?locale=en_US&cta=v3invoicelink",
							"business_name":"AM Healthcare Enterprise LTD",
							"invoice_id":"7291",
							"invoice_number":"7291",
							"invoice_date":"08/15/2023",
							"invoice_due_date":"08/15/2023",
							"overdue_days":"695",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"23430.65",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:00:52:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"MzA2YWE4MGEtNWQ2OC0xMWYwLWEyY2YtMWE0MDg3YzE3OGI0LTgxNGUxZTU4OQ","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:00:53:Reminder email Note added to project and lead sent data:{"lead_id":"2060","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7291","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:00:53:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"2060","note_id":215829}-----------------------------
2025-07-10 14:00:53: Reminder triggered for invoice no:5827
------------------------------
2025-07-10 14:00:53:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Charles Siritho",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=eda5f868cc35cccbe6ea2f88a54af6e7",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-881b081db8044f7483d8a98446c6d7ca75412d1b78be40bba14ecb145536861d32cb5fea2c6d46eaafa929de13dcab61?locale=en_US&cta=v3invoicelink",
							"business_name":"TFFP LLC",
							"invoice_id":"7287",
							"invoice_number":"7287",
							"invoice_date":"07/27/2023",
							"invoice_due_date":"08/07/2023",
							"overdue_days":"703",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"7544.94",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:00:54:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"MzE4YTQwY2YtNWQ2OC0xMWYwLWI4OWMtYmE0YjYxNGJhM2ViLWVhZGEzMTRlYg","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:00:55:Reminder email Note added to project and lead sent data:{"lead_id":"2369","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7287","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:00:55:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"2369","note_id":215830}------------------ AT
2025-07-10 14:00:55: Reminder not send for payment plan lead, terminated the loop for invoice no:5798 and lead id:2177------------------ AT
2025-07-10 14:00:55: Reminder not send for payment plan lead, terminated the loop for invoice no:5785 and lead id:1964------------------ AT
2025-07-10 14:00:55: Reminder not send for payment plan lead, terminated the loop for invoice no:5784 and lead id:720------------------ AT
2025-07-10 14:00:55: Reminder not send for payment plan lead, terminated the loop for invoice no:5780 and lead id:1919-----------------------------
2025-07-10 14:00:55: Reminder triggered for invoice no:5778
------------------------------
2025-07-10 14:00:55:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"John Garcia",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=ae56740ea9219263e1b5684c9b3e7035",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-81fa76bee2eb44c8ae4a0926866bba551503e64956d34f9da36c15021d7f51870b2c316d436b4f8099d9692a909d2f7d?locale=en_US&cta=v3invoicelink",
							"business_name":"Ocean Granada Inc",
							"invoice_id":"7238",
							"invoice_number":"7238",
							"invoice_date":"07/24/2023",
							"invoice_due_date":"08/03/2023",
							"overdue_days":"707",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"1063.88",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:00:56:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"MzJjMzI1OTctNWQ2OC0xMWYwLWJhNWYtYmE4Y2JlZDQ4ZTc2LTcyYmRkMjZhNA","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:00:57:Reminder email Note added to project and lead sent data:{"lead_id":"2329","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7238","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:00:57:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"2329","note_id":215831}-----------------------------
2025-07-10 14:00:57: Reminder triggered for invoice no:5777
------------------------------
2025-07-10 14:00:57:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"John Garcia",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=ae999cd3b411fbc1c404b3d71fabbbfd",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-17b0636faa9641fbad4290fb32720f470c65f2b24a1f4ce1b5994d93fbce02719118a3112e64439c8b4ef9d375de6138?locale=en_US&cta=v3invoicelink",
							"business_name":"Ocean Granada Inc",
							"invoice_id":"7237",
							"invoice_number":"7237",
							"invoice_date":"07/18/2023",
							"invoice_due_date":"07/28/2023",
							"overdue_days":"713",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"2713.54",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:00:58:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"MzNlNmUzNjgtNWQ2OC0xMWYwLWFiNDEtYmE0YjYxNGJhM2ViLTc2NTI4NmQyNQ","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:00:59:Reminder email Note added to project and lead sent data:{"lead_id":"2329","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7237","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:00:59:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"2329","note_id":215832}-----------------------------
2025-07-10 14:00:59: Reminder triggered for invoice no:5775
------------------------------
2025-07-10 14:00:59:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Toby Holland",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=d316bd23e27969e32b5963d4e8b8e325",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-c311b8f075e84501b23516ea6d354009d61b896484cc40d6835da0d8b8c16457f2895152dafe4016abb5b3737966bb0f?locale=en_US&cta=v3invoicelink",
							"business_name":"Interstate Tire And Lube, Inc.",
							"invoice_id":"7235",
							"invoice_number":"7235",
							"invoice_date":"07/24/2023",
							"invoice_due_date":"08/03/2023",
							"overdue_days":"707",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"7184.95",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:01:00:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"MzUwYzM3MDItNWQ2OC0xMWYwLTg3ZGItODI5OWYyYzEwZDliLWJmNGE3YWM3ZQ","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:01:00:Reminder email Note added to project and lead sent data:{"lead_id":"2439","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7235","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:01:00:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"2439","note_id":215834}-----------------------------
2025-07-10 14:01:01: Reminder triggered for invoice no:5774
------------------------------
2025-07-10 14:01:01:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Toby Holland",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=6e5c01b7b2c150a9585e2686a0a5c452",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-5d095ead94de404a99950ad1845190922e3fc73301c34326a63b194d52d0d34f55933ebc5d7d47299d35b914205020a3?locale=en_US&cta=v3invoicelink",
							"business_name":"Interstate Tire And Lube, Inc.",
							"invoice_id":"7234",
							"invoice_number":"7234",
							"invoice_date":"07/18/2023",
							"invoice_due_date":"07/28/2023",
							"overdue_days":"713",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"18726.12",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:01:02:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"MzYyYTM1MjYtNWQ2OC0xMWYwLTgzN2YtYmE0YjYxNGJhM2ViLTJjY2JlOGNmZg","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:01:02:Reminder email Note added to project and lead sent data:{"lead_id":"2439","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7234","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:01:02:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"2439","note_id":215835}-----------------------------
2025-07-10 14:01:03: Reminder triggered for invoice no:5773
------------------------------
2025-07-10 14:01:03:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Matt Smith",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=008aa51302fb4ad7239adadfe3be8603",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-24ac01db194b459091e8620f95790b95a821ea5690964574b9e6b20c0e7b2e300862222f05a64b38998b03cecb8aca7a?locale=en_US&cta=v3invoicelink",
							"business_name":"Credence Construction LLC- CCB",
							"invoice_id":"7233",
							"invoice_number":"7233",
							"invoice_date":"06/20/2023",
							"invoice_due_date":"06/30/2023",
							"overdue_days":"741",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"21966.96",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:01:04:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"Mzc1MGEwZGUtNWQ2OC0xMWYwLWEyZTctNWUyMzZlNmNlYzU5LTdkNWQwZDBmOA","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:01:04:Reminder email Note added to project and lead sent data:{"lead_id":"1938","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7233","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:01:04:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"1938","note_id":215836}------------------ AT
2025-07-10 14:01:05: Reminder not send for payment plan lead, terminated the loop for invoice no:5771 and lead id:1998------------------ AT
2025-07-10 14:01:05: Reminder not send for payment plan lead, terminated the loop for invoice no:5770 and lead id:1998-----------------------------
2025-07-10 14:01:05: Reminder triggered for invoice no:5768
------------------------------
2025-07-10 14:01:05:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Lawrence A",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=c75c3360ddc8f19790283ecfca4141ec",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-5b043afa289743d7a0f6e929f343cfa9a4f20779fb1942b089c03d2086ab76b16b67351fa561452b851c6c6f8528ddb7?locale=en_US&cta=v3invoicelink",
							"business_name":"Aldridge Services Inc.",
							"invoice_id":"7228",
							"invoice_number":"7228",
							"invoice_date":"10/14/2024",
							"invoice_due_date":"10/14/2024",
							"overdue_days":"269",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"120.35",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:01:05:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"Mzg3Njg3NDUtNWQ2OC0xMWYwLWFhZmItMjZjYzRiZGVlNDViLWNiNTQwMzVmYg","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:01:06:Reminder email Note added to project and lead sent data:{"lead_id":"1650","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7228","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:01:06:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"1650","note_id":215837}-----------------------------
2025-07-10 14:01:06: Reminder triggered for invoice no:5725
------------------------------
2025-07-10 14:01:06:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Shaney Whiting",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=41b6d16558e014d369edbaab9761b5e2",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-821090bf55df4aeca5048c3b9afbd30cbef7ba95cb774a90a3b8921b252f10d5e16740af4568450fa23495b248a78e2b?locale=en_US&cta=v3invoicelink",
							"business_name":"SDE Enterprises LLC",
							"invoice_id":"7185",
							"invoice_number":"7185",
							"invoice_date":"04/14/2025",
							"invoice_due_date":"04/14/2025",
							"overdue_days":"87",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"4288.68",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:01:07:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"Mzk5Njc4MDAtNWQ2OC0xMWYwLWFlMDktODIxMjU4MGViNmQxLTI1YTA1NDlhYQ","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:01:08:Reminder email Note added to project and lead sent data:{"lead_id":"5715","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7185","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:01:08:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"5715","note_id":215839}-----------------------------
2025-07-10 14:01:08: Reminder triggered for invoice no:5722
------------------------------
2025-07-10 14:01:08:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Andrew Baker",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=f1efb4a5934683d7f847fdaab1a04b17",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-7ce6212b88ad4034af6c54e8bc8946dcfcf3c143234b47719f7f8c09c21fa1601983e93e3ba04889b8134171a66b889d?locale=en_US&cta=v3invoicelink",
							"business_name":"Reliant Transportation Group LLC",
							"invoice_id":"7182",
							"invoice_number":"7182",
							"invoice_date":"04/14/2025",
							"invoice_due_date":"04/14/2025",
							"overdue_days":"87",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"2610.93",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:01:09:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"M2FiOGMxNjYtNWQ2OC0xMWYwLWIwMjktNTZiOTViZmQ4ZmY5LTJhMjU0NTE5NA","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:01:10:Reminder email Note added to project and lead sent data:{"lead_id":"7055","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7182","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:01:10:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"7055","note_id":215840}-----------------------------
2025-07-10 14:01:10: Reminder triggered for invoice no:5715
------------------------------
2025-07-10 14:01:10:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Hannah Oliver Depp",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=0fd551ce558c0898be4d66708d04f86b",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-d4c755b2138b4b1e9f089ca52c3520344da7b5b084614673a7881107d9139ecdb5fd0f36c4ee4de8a93ccb659d9fb4de?locale=en_US&cta=v3invoicelink",
							"business_name":"Loyalty Books LLC",
							"invoice_id":"7175",
							"invoice_number":"7175",
							"invoice_date":"04/14/2025",
							"invoice_due_date":"04/14/2025",
							"overdue_days":"87",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"13960.49",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:01:11:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"M2JlN2I5MDAtNWQ2OC0xMWYwLWEwNDktNmFlNGI1NzMxODI2LWRjYzNhMjQwNg","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:01:12:Reminder email Note added to project and lead sent data:{"lead_id":"8073","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7175","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:01:12:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"8073","note_id":215841}-----------------------------
2025-07-10 14:01:12: Reminder triggered for invoice no:5711
------------------------------
2025-07-10 14:01:12:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Lisa Burnett",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=901d060862e728e86a356cfdb36cb39d",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-b88941104c6640b283124dde852276ca88ed2ea98d7e4543b72b73ed20ddb118fde160b218a0418ab4261bf323305f72?locale=en_US&cta=v3invoicelink",
							"business_name":"LRB Home Health Care Agency",
							"invoice_id":"7171",
							"invoice_number":"7171",
							"invoice_date":"04/14/2025",
							"invoice_due_date":"04/14/2025",
							"overdue_days":"87",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"4887.98",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:01:13:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"M2QxNmUzNzAtNWQ2OC0xMWYwLWE1NzQtYmExMTUyMmFlZGMwLTBlZjc3M2VlMw","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:01:14:Reminder email Note added to project and lead sent data:{"lead_id":"2056","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7171","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:01:14:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"2056","note_id":215842}-----------------------------
2025-07-10 14:01:14: Reminder triggered for invoice no:5710
------------------------------
2025-07-10 14:01:14:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Kimberly Caldwell",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=09c300b6e88cf0fb4d5abb7c113f141b",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-d964c6f60f63455d8dacaffaac5b314040f2e8d7f6d840a98a26c398dbdfcaa27f5a06538eb148f2bdf6e7618f33599d?locale=en_US&cta=v3invoicelink",
							"business_name":"Laycock Hobbs",
							"invoice_id":"7170",
							"invoice_number":"7170",
							"invoice_date":"04/14/2025",
							"invoice_due_date":"04/14/2025",
							"overdue_days":"87",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"2488.38",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:01:15:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"M2U0NDBmOGYtNWQ2OC0xMWYwLThjNjUtMjZjYzRiZGVlNDViLTE3NzBkZWNlNg","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:01:16:Reminder email Note added to project and lead sent data:{"lead_id":"3999","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7170","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:01:16:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"3999","note_id":215844}-----------------------------
2025-07-10 14:01:16: Reminder triggered for invoice no:5683
------------------------------
2025-07-10 14:01:16:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Vikas Desai",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=d692561eedd996b764c6210ebc88ad00",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-4898250c4e2348989def0586e1538924a158f7c7071d4762a233cb88a86091ff2e96ab01a04c4e728028077d8ca086b8?locale=en_US&cta=v3invoicelink",
							"business_name":"Vikas Desai",
							"invoice_id":"7143",
							"invoice_number":"7143",
							"invoice_date":"04/04/2025",
							"invoice_due_date":"04/04/2025",
							"overdue_days":"97",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"40887.22",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:01:17:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"M2Y2NmViZDQtNWQ2OC0xMWYwLWE1MjEtM2VhMGM4MjhmNDAyLTk4NDU5YTQ5Yw","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:01:18:Reminder email Note added to project and lead sent data:{"lead_id":"9095","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7143","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:01:18:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"9095","note_id":215845}-----------------------------
2025-07-10 14:01:18: Reminder triggered for invoice no:5661
------------------------------
2025-07-10 14:01:18:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Kevin Longacker",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=b0dfcc8b4bcb86c4455384094d18476c",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-bf76a491de17408b9fcb6f2eb7ca6b294d661c180af046d4b823d7777ccc65ecfc9990e07a514a18a6ad71719bbb6058?locale=en_US&cta=v3invoicelink",
							"business_name":"EVG VETERINARIANS OF VIRGINIA PLLC",
							"invoice_id":"7121",
							"invoice_number":"7121",
							"invoice_date":"04/02/2025",
							"invoice_due_date":"04/02/2025",
							"overdue_days":"99",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"14349.05",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:01:19:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"NDA4OTMzOGMtNWQ2OC0xMWYwLWEwNWYtN2ExZjk4ZjY5NGVmLTk1NDRmYTM5NQ","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:01:20:Reminder email Note added to project and lead sent data:{"lead_id":"6503","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7121","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:01:20:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"6503","note_id":215846}-----------------------------
2025-07-10 14:01:20: Reminder triggered for invoice no:5593
------------------------------
2025-07-10 14:01:20:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Henry L Ruiz Jr",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=9f32ac772e2d65e084bcd44655f7c6d2",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-2da47b7f25f74089b359ad58d6bdc474345f6e2341a848baab2d7a54c78789a1ec6393afc32e474b88299744328d809c?locale=en_US&cta=v3invoicelink",
							"business_name":"HSMR, INC.",
							"invoice_id":"7054",
							"invoice_number":"7054",
							"invoice_date":"03/03/2025",
							"invoice_due_date":"03/03/2025",
							"overdue_days":"129",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"9157.93",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:01:21:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"NDFiNTBmMTktNWQ2OC0xMWYwLWEwYTYtNzY1MmUxMDUwZGMyLWQ5NDMxY2Q1Ng","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:01:22:Reminder email Note added to project and lead sent data:{"lead_id":"4785","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7054","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:01:22:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"4785","note_id":215847}-----------------------------
2025-07-10 14:01:22: Reminder triggered for invoice no:5589
------------------------------
2025-07-10 14:01:22:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Kevin Longhacker",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=e3fcfe84dbcdbebd28d2fb43d107172f",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-febff72abecb44878c292a7f4ccf24c3724350a472a749c99a959d9ff4459f0e68751a5700a14f61828875ed3fc1668e?locale=en_US&cta=v3invoicelink",
							"business_name":"EVG VETERINARIANS OF NORTH CAROLINA",
							"invoice_id":"7050",
							"invoice_number":"7050",
							"invoice_date":"03/17/2025",
							"invoice_due_date":"03/17/2025",
							"overdue_days":"115",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"535.47",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:01:23:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"NDJkNTVmNmItNWQ2OC0xMWYwLTgzZjQtZmU1OTkyNWYwOWU2LTNiNTRlMTdmNA","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:01:24:Reminder email Note added to project and lead sent data:{"lead_id":"6496","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7050","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:01:24:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"6496","note_id":215849}-----------------------------
2025-07-10 14:01:24: Reminder triggered for invoice no:5568
------------------------------
2025-07-10 14:01:24:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Kevin Longacker",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=d8566028b9d1e18b799f831f21a00add",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-150762016ddb4580951a1abd8284b3228db3fd7409a94132896415d32197e0540f5f55d2d1b54fc68fa77eb649e6c2d9?locale=en_US&cta=v3invoicelink",
							"business_name":"WEST CHELSEA VETERINARY CARE PLLC",
							"invoice_id":"7029",
							"invoice_number":"7029",
							"invoice_date":"03/17/2025",
							"invoice_due_date":"03/17/2025",
							"overdue_days":"115",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"8936.23",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:01:25:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"NDNmNTI3ZTEtNWQ2OC0xMWYwLWIzZDktNTI5Njc5MTZiNmMzLTNhNGE0YTVmZA","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:01:25:Reminder email Note added to project and lead sent data:{"lead_id":"6507","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7029","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:01:25:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"6507","note_id":215850}-----------------------------
2025-07-10 14:01:26: Reminder triggered for invoice no:5547
------------------------------
2025-07-10 14:01:26:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Saurin Patel",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=4346e02db24231ca34ab04d5557b5821",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-67f5b39130134219844fc485a9e93930288bc823e346401e9a420f26dda8066ab39aee51ec4740d58f6ad016f0cf9514?locale=en_US&cta=v3invoicelink",
							"business_name":"Mangal Corporation",
							"invoice_id":"7008",
							"invoice_number":"7008",
							"invoice_date":"03/17/2025",
							"invoice_due_date":"03/17/2025",
							"overdue_days":"115",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"1318.24",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:01:27:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"NDUxM2VlNWUtNWQ2OC0xMWYwLTkyZDMtZmFjZjU5MDAwYzA1LTdiODY0ZDhlNg","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:01:27:Reminder email Note added to project and lead sent data:{"lead_id":"4107","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7008","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:01:27:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"4107","note_id":215851}-----------------------------
2025-07-10 14:01:28: Reminder triggered for invoice no:5540
------------------------------
2025-07-10 14:01:28:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Russell Ouchida",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=df03033b4a35f81581dd32c2b0504917",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-9dd483bbdb794580b3b9942f0820e97e77b4a0d947194483a49780ba2547e7fc206e1ac910c4488dba18d7d1f29897a2?locale=en_US&cta=v3invoicelink",
							"business_name":"Karum Group LLC",
							"invoice_id":"7001",
							"invoice_number":"7001",
							"invoice_date":"03/17/2025",
							"invoice_due_date":"03/17/2025",
							"overdue_days":"115",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"10923.41",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:01:29:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"NDYzNzM1ODctNWQ2OC0xMWYwLWFlYzMtNjZkYmVmNDY3YzM4LTk0MThhNzQ0Nw","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:01:29:Reminder email Note added to project and lead sent data:{"lead_id":"5674","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #7001","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:01:29:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"5674","note_id":215852}-----------------------------
2025-07-10 14:01:30: Reminder triggered for invoice no:5520
------------------------------
2025-07-10 14:01:30:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Kevin Longacker",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=c5ab319df4859b230bb4e6b1b5d20ae0",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-1071834b07f4443d80715db1e393c31f57f7f4366df346ceb100f1f695acd89d12541fbd3b8f47549b27e0a6bcc18ebf?locale=en_US&cta=v3invoicelink",
							"business_name":"EVG VETERINARIANS OF WISCONSIN SC",
							"invoice_id":"6981",
							"invoice_number":"6981",
							"invoice_date":"03/17/2025",
							"invoice_due_date":"03/17/2025",
							"overdue_days":"115",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"14854.53",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:01:30:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"NDc1YzU2OWUtNWQ2OC0xMWYwLWI5MDYtZmU1OTkyNWYwOWU2LWU1YTFmODA3Mg","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:01:31:Reminder email Note added to project and lead sent data:{"lead_id":"6506","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #6981","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:01:31:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"6506","note_id":215854}-----------------------------
2025-07-10 14:01:31: Reminder triggered for invoice no:5518
------------------------------
2025-07-10 14:01:31:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Kevin Longhacker",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=60508b1cc52f3b39baf1f1e73edd4aeb",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-0a40f0dfed0545aa96c8027f8dd4a56109372997bd534137ac5fe2bee5ccd81af089275ca6c04e77ae2b7336fbf27dad?locale=en_US&cta=v3invoicelink",
							"business_name":"EVG VETERINARIANS OF VSRP PLLC",
							"invoice_id":"6979",
							"invoice_number":"6979",
							"invoice_date":"03/17/2025",
							"invoice_due_date":"03/17/2025",
							"overdue_days":"115",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"1238.65",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:01:32:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"NDg4Mjc1OWUtNWQ2OC0xMWYwLTkzMmItYWE2NmRjMjE2NzcxLTU4ZDEzZDA5NA","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:01:33:Reminder email Note added to project and lead sent data:{"lead_id":"6505","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #6979","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:01:33:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"6505","note_id":215855}-----------------------------
2025-07-10 14:01:33: Reminder triggered for invoice no:5517
------------------------------
2025-07-10 14:01:33:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Kevin Longacker",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=030bac607e86c416d102ab8ca1c0c512",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-03c23903ab84481bb959b7428daf45658e53fd4da6114b87bb8538c54d2616bc9c37d82ced8547309e3cc794e8d08910?locale=en_US&cta=v3invoicelink",
							"business_name":"EVG VETERINARIANS OF VIRGINIA PLLC",
							"invoice_id":"6978",
							"invoice_number":"6978",
							"invoice_date":"03/17/2025",
							"invoice_due_date":"03/17/2025",
							"overdue_days":"115",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"370.43",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:01:34:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"NDlhMjY0ODItNWQ2OC0xMWYwLThjMzAtNzY1MmUxMDUwZGMyLWQ1NGJhNjkxYQ","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:01:35:Reminder email Note added to project and lead sent data:{"lead_id":"6503","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #6978","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:01:35:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"6503","note_id":215856}-----------------------------
2025-07-10 14:01:35: Reminder triggered for invoice no:5513
------------------------------
2025-07-10 14:01:35:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Kevin Longhacker",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=7350d15bbc87e761f22c6c45c6b17faa",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-cde0f8a8cb9b465890aae458eb52d8fea54b9ccf143046b4a016d5cabe35ca915935e4e68c924a4abaae1ea90de5d6ee?locale=en_US&cta=v3invoicelink",
							"business_name":"EVG VETERINARIANS OF NORTH ELM PLLC",
							"invoice_id":"6974",
							"invoice_number":"6974",
							"invoice_date":"03/17/2025",
							"invoice_due_date":"03/17/2025",
							"overdue_days":"115",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"9542.77",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:01:36:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"NGFiZWU1N2QtNWQ2OC0xMWYwLThlMWMtMmFmNGY2ZDZiZjU1LTBlMWIzZTFkMA","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:01:37:Reminder email Note added to project and lead sent data:{"lead_id":"6497","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #6974","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:01:37:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"6497","note_id":215857}-----------------------------
2025-07-10 14:01:37: Reminder triggered for invoice no:5511
------------------------------
2025-07-10 14:01:37:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Kevin Longhacker",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=ba61566341dd149c39549c873485abf5",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-b913b6ff8d5341958166e961994f4e9d21c902f9c37a41ed82a0df59b314b3e92b66038fb7ec41fa865b8828016accee?locale=en_US&cta=v3invoicelink",
							"business_name":"EVG VETERINARIANS OF MICHIGAN PLLC",
							"invoice_id":"6972",
							"invoice_number":"6972",
							"invoice_date":"03/17/2025",
							"invoice_due_date":"03/17/2025",
							"overdue_days":"115",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"12515.80",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:01:38:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"NGJlMTc2MDUtNWQ2OC0xMWYwLTlkOGUtNmU5MjdlMzA4YmJhLTQwOWNkNTEyZA","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:01:39:Reminder email Note added to project and lead sent data:{"lead_id":"6494","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #6972","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:01:39:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"6494","note_id":215859}-----------------------------
2025-07-10 14:01:39: Reminder triggered for invoice no:5506
------------------------------
2025-07-10 14:01:39:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Kevin Longhacker",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=20d6b2a4f32b498c5be21d452b30d5ac",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-dee96d3e8f9441b4bf1ae41be4fcbd701108d1873b154f24aad90194994701f43497c634701c4c1381d1f4a681b232fe?locale=en_US&cta=v3invoicelink",
							"business_name":"ENCORE MANAGEMENT GROUP OF OHIO",
							"invoice_id":"6967",
							"invoice_number":"6967",
							"invoice_date":"03/17/2025",
							"invoice_due_date":"03/17/2025",
							"overdue_days":"115",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"109276.90",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:01:40:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"NGQwZjBmMzMtNWQ2OC0xMWYwLWJjZDYtNGFlYjI0NjRkMWMxLTBlOGNiYzIwMw","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:01:41:Reminder email Note added to project and lead sent data:{"lead_id":"6487","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #6967","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:01:41:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"6487","note_id":215860}-----------------------------
2025-07-10 14:01:41: Reminder triggered for invoice no:5505
------------------------------
2025-07-10 14:01:41:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Kevin Longhacker",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=d46c6632ccea69f5eed83f6c2a7c105c",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-3ed0ac3d4d0346d592dbf3caf4dc6bb2e3a586c7df8946cc822177558ea63eead396020336834820886938553b8b090a?locale=en_US&cta=v3invoicelink",
							"business_name":"ENCORE MANAGEMENT GROUP OF NORTH",
							"invoice_id":"6966",
							"invoice_number":"6966",
							"invoice_date":"03/17/2025",
							"invoice_due_date":"03/17/2025",
							"overdue_days":"115",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"15383.04",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:01:43:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"NGUyZDJkYzAtNWQ2OC0xMWYwLWIxYjAtNTI5Njc5MTZiNmMzLWNmNjcyNTJjZQ","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:01:44:Reminder email Note added to project and lead sent data:{"lead_id":"6486","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #6966","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:01:44:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"6486","note_id":215861}------------------ AT
2025-07-10 14:01:44: Reminder not send for payment plan lead, terminated the loop for invoice no:5440 and lead id:1919-----------------------------
2025-07-10 14:01:44: Reminder triggered for invoice no:5303
------------------------------
2025-07-10 14:01:44:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"John Stopoulos",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=7a080ebb10248950869205b192a5ba91",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-12909a7d39bf45568db7b18e2b93d0810be870693eef44b6948f39e877b2548ed9771f4369a74ec19cf59dd350f70d3c?locale=en_US&cta=v3invoicelink",
							"business_name":"Fifth Avenue Maid Rite Inc",
							"invoice_id":"6764",
							"invoice_number":"6764",
							"invoice_date":"03/03/2025",
							"invoice_due_date":"03/03/2025",
							"overdue_days":"129",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"10263.28",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:01:47:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"NTA4NTcxMmUtNWQ2OC0xMWYwLWJmYzQtMjZjYzRiZGVlNDViLTdhNGE5MmJlZg","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:01:48:Reminder email Note added to project and lead sent data:{"lead_id":"3341","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #6764","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:01:48:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"3341","note_id":215863}-----------------------------
2025-07-10 14:01:48: Reminder triggered for invoice no:5295
------------------------------
2025-07-10 14:01:48:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Angelina Masini",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=ade26d2d51ff832e9dfb6eb1e01f307e",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-8e534450233f42f388f4e5e77be522a19e41ea03898c41439eaef952f00e6cf2e87816fcb41c4e04bbe802ddce26028e?locale=en_US&cta=v3invoicelink",
							"business_name":"Swampland Diesel & Automotive LLC",
							"invoice_id":"6756",
							"invoice_number":"6756",
							"invoice_date":"03/08/2024",
							"invoice_due_date":"03/18/2024",
							"overdue_days":"479",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"4355.16",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:01:51:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"NTJmYmI4YmQtNWQ2OC0xMWYwLThlZTYtZmU1OTkyNWYwOWU2LTM2MzhhMGRmYw","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:01:52:Reminder email Note added to project and lead sent data:{"lead_id":"5159","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #6756","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:01:52:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"5159","note_id":215864}-----------------------------
2025-07-10 14:01:52: Reminder triggered for invoice no:5294
------------------------------
2025-07-10 14:01:52:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Randy Berner",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=584a1cea2b576737d59c8ff05cb6aa45",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-cb18cb42752d41dca4684bcf79383c3c28a9e9f3c8324e069308adef8311297d92729ca03276469a8fa591b736954992?locale=en_US&cta=v3invoicelink",
							"business_name":"PEO - Builders Source LLC (Enterprise HR/EncoreHR)",
							"invoice_id":"6755",
							"invoice_number":"6755",
							"invoice_date":"08/01/2023",
							"invoice_due_date":"08/01/2023",
							"overdue_days":"709",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"8950.41",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:01:56:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"NTU1Y2MwYmQtNWQ2OC0xMWYwLThkM2MtNjJhMjVkNGRjODcwLWIwNTZlZTdhNA","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:01:56:Reminder email Note added to project and lead sent data:{"lead_id":"2667","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #6755","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:01:56:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"2667","note_id":215866}-----------------------------
2025-07-10 14:01:57: Reminder triggered for invoice no:5293
------------------------------
2025-07-10 14:01:57:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Michael Robinson",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=5c7a776f45af602dbf19cf4425c8f9e8",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-c0b98134f23c4bfa86cca231114fe834429b649b7794471493d6bae2c70ddc7ef478c1cdd09f4f0fa18319510da4f911?locale=en_US&cta=v3invoicelink",
							"business_name":"Harrisburg Animal Hospital, PA",
							"invoice_id":"6754",
							"invoice_number":"6754",
							"invoice_date":"04/01/2024",
							"invoice_due_date":"04/03/2024",
							"overdue_days":"463",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"17663.01",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:02:00:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"NTdkYjI5ZTYtNWQ2OC0xMWYwLWFiMmQtOGU0NDViZjg0NzQxLTU1ZWY2YWJjOQ","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:02:00:Reminder email Note added to project and lead sent data:{"lead_id":"6310","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #6754","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:02:00:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"6310","note_id":215867}-----------------------------
2025-07-10 14:02:01: Reminder triggered for invoice no:5292
------------------------------
2025-07-10 14:02:01:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Michael Robinson",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=a3e24655fbf553dcabe2e774a0934732",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-7e0d4bc9a59348dca868b79265f3fe67cf177c752d2d40c3869ba30181fdc3204217eb0ca0fb4720bc9a90931f779e32?locale=en_US&cta=v3invoicelink",
							"business_name":"Cabarrus Veterinary Hospital, PA",
							"invoice_id":"6753",
							"invoice_number":"6753",
							"invoice_date":"04/01/2024",
							"invoice_due_date":"04/03/2024",
							"overdue_days":"463",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"26494.52",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:02:04:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"NWE0ZTNkNTctNWQ2OC0xMWYwLTkyZWQtMjZjYzRiZGVlNDViLTQ0ZWE3ZjliYg","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:02:04:Reminder email Note added to project and lead sent data:{"lead_id":"5992","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #6753","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:02:04:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"5992","note_id":215869}------------------ AT
2025-07-10 14:02:05: Reminder not send for payment plan lead, terminated the loop for invoice no:5291 and lead id:1714-----------------------------
2025-07-10 14:02:05: Reminder triggered for invoice no:5289
------------------------------
2025-07-10 14:02:05:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Vicki Lindgren",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=4e4978d11291eb162536401254876aa2",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-9ceaf6e2d7bc4540a57b9db41abd157a9005bbf48e2e4e9f9422fdcd4da5eb45d30e4a751e0e4b9aad811fcb69dda5df?locale=en_US&cta=v3invoicelink",
							"business_name":"Chandler Handlers INC",
							"invoice_id":"6750",
							"invoice_number":"6750",
							"invoice_date":"01/02/2024",
							"invoice_due_date":"01/02/2024",
							"overdue_days":"555",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"1826.48",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:02:08:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"NWNiMWQzZmEtNWQ2OC0xMWYwLTkxZWItNmVlNTcyMTY2YmRhLWMyM2MyYTg2ZQ","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:02:09:Reminder email Note added to project and lead sent data:{"lead_id":"456","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #6750","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:02:09:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"456","note_id":215870}-----------------------------
2025-07-10 14:02:09: Reminder triggered for invoice no:5288
------------------------------
2025-07-10 14:02:09:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Brian Reale",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=6bfbf2c3e33645fe9def57002a9641e3",
							"pay_invoice_url":"",
							"business_name":"BAT INC DBA Coast Processing",
							"invoice_id":"6749",
							"invoice_number":"6749",
							"invoice_date":"04/15/2023",
							"invoice_due_date":"04/25/2023",
							"overdue_days":"807",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"273793.94",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:02:12:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"NWYzYmI1NTEtNWQ2OC0xMWYwLWExNDctY2EzNGU3MDI2YzQzLTJkNDQ2NDVhMA","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:02:13:Reminder email Note added to project and lead sent data:{"lead_id":"575","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #6749","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:02:13:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"575","note_id":215871}------------------ AT
2025-07-10 14:02:13: Reminder not send for payment plan lead, terminated the loop for invoice no:5250 and lead id:4582-----------------------------
2025-07-10 14:02:13: Reminder triggered for invoice no:5202
------------------------------
2025-07-10 14:02:13:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Lucy Garcia",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=4422f220472408b87a4667f3b758b6b0",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-989cf75d66084147b84bcd7f720d112f5c7c73b654af42048f4250a49cd697c85653df9038fe494eacc1329cbdf0568f?locale=en_US&cta=v3invoicelink",
							"business_name":"Orange View Healthcare, Inc. - CCB",
							"invoice_id":"6663",
							"invoice_number":"6663",
							"invoice_date":"07/18/2023",
							"invoice_due_date":"07/28/2023",
							"overdue_days":"713",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"4469.85",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:02:16:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"NjFhODU5MTEtNWQ2OC0xMWYwLWI5ODUtN2ExZjk4ZjY5NGVmLTNhOWUyNmViNg","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:02:17:Reminder email Note added to project and lead sent data:{"lead_id":"2238","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #6663","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:02:17:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"2238","note_id":215873}------------------ AT
2025-07-10 14:02:17: Reminder not send for payment plan lead, terminated the loop for invoice no:5201 and lead id:2332------------------ AT
2025-07-10 14:02:17: Reminder not send for payment plan lead, terminated the loop for invoice no:5191 and lead id:720-----------------------------
2025-07-10 14:02:17: Reminder triggered for invoice no:5190
------------------------------
2025-07-10 14:02:17:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Donald Love",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=691fa10aeca5e297d83c3d7e1b3df940",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-913755900f1e4dc799986b0af76a1093444d4651e01642c8bfb091419868cee3dc055484b3a84ca2a4d656e4e205f19b?locale=en_US&cta=v3invoicelink",
							"business_name":"Inland Empire Community Outreach - CCB",
							"invoice_id":"6651",
							"invoice_number":"6651",
							"invoice_date":"04/05/2023",
							"invoice_due_date":"04/15/2023",
							"overdue_days":"817",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"2594.71",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:02:18:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"NjNjMzg3YzUtNWQ2OC0xMWYwLWI1MzAtMGUxM2NlOGVhNmEzLTI0YmYyNDYwMw","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:02:19:Reminder email Note added to project and lead sent data:{"lead_id":"613","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #6651","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:02:19:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"613","note_id":215874}-----------------------------
2025-07-10 14:02:19: Reminder triggered for invoice no:5189
------------------------------
2025-07-10 14:02:19:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Carol Wright",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=fd5b6bc71893ab850a9e52b0caa48d49",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-1ff899a57d814e8ca8e74b61e16842ae25fe6c0655264cfca5ce4b4d7e6073ecb22875b482244cf482dd4eaf6c8e8296?locale=en_US&cta=v3invoicelink",
							"business_name":"Home Care Professionals LLC",
							"invoice_id":"6650",
							"invoice_number":"6650",
							"invoice_date":"01/02/2024",
							"invoice_due_date":"01/12/2024",
							"overdue_days":"545",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"30734.08",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:02:20:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"NjUwNzdlOTQtNWQ2OC0xMWYwLTllYTctY2EzNGU3MDI2YzQzLWRhZWIxYjQxZA","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:02:21:Reminder email Note added to project and lead sent data:{"lead_id":"489","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #6650","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:02:21:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"489","note_id":215875}-----------------------------
2025-07-10 14:02:21: Reminder triggered for invoice no:5188
------------------------------
2025-07-10 14:02:21:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>",
						"bcc":"",
						"data": {
							"customer_name":"Nadria Phillips",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=8a9f55436d5df2c1203380843f76f929",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-783a4e66091a435fb9ddf7413203b9886164a484386d4c1ca323a9e14f39d7fddd42985bc84d4425be8f629521ee15bf?locale=en_US&cta=v3invoicelink",
							"business_name":"Benevolent Hearts",
							"invoice_id":"6649",
							"invoice_number":"6649",
							"invoice_date":"01/28/2025",
							"invoice_due_date":"01/28/2025",
							"overdue_days":"163",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"1820.33",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:02:22:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"NjYzMDQ2MWUtNWQ2OC0xMWYwLWFkYWQtNmFlNGI1NzMxODI2LTc1Nzk4YTY4MA","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:02:23:Reminder email Note added to project and lead sent data:{"lead_id":"1089","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #6649","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:02:23:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"1089","note_id":215877}-----------------------------
2025-07-10 14:02:23: Reminder triggered for invoice no:5153
------------------------------
2025-07-10 14:02:23:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>",
						"bcc":"<EMAIL>",
						"data": {
							"customer_name":"Cherry Lamb",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=8ad8c5a4e6916ca341646050e4509a14",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-d60b2f0ba4ce4205a50856b808673696dd3d6ead8320464596505ee394c9b8cc754fc8faae88466bb493ec40c87d1ad9?locale=en_US&cta=v3invoicelink",
							"business_name":"RCP America Inc",
							"invoice_id":"6614",
							"invoice_number":"6614",
							"invoice_date":"01/02/2024",
							"invoice_due_date":"01/02/2024",
							"overdue_days":"555",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"60628.47",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:02:24:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"Njc1YjY2NWUtNWQ2OC0xMWYwLWFhYjYtYWFlMjNhYWM2NjgxLTkxNTRjZmIwNQ","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:02:25:Reminder email Note added to project and lead sent data:{"lead_id":"1589","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #6614","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:02:25:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"1589","note_id":215878}-----------------------------
2025-07-10 14:02:25: Reminder triggered for invoice no:5101
------------------------------
2025-07-10 14:02:25:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>",
						"bcc":"<EMAIL>",
						"data": {
							"customer_name":"Donna M Monahan",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=844b15866e001c95d5d23f420c43b2b6",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-eb91018ff9b74a8abbf9d8ef90819df054c38f012bae49738ef29351352bfeab605d5c784e8040e187c7620b06ddd3aa?locale=en_US&cta=v3invoicelink",
							"business_name":"Edge Tech Staffing Inc",
							"invoice_id":"6562",
							"invoice_number":"6562",
							"invoice_date":"01/06/2025",
							"invoice_due_date":"01/06/2025",
							"overdue_days":"185",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"19615.71",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:02:26:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"Njg3NzI3YmQtNWQ2OC0xMWYwLWFhM2YtOGU0NDViZjg0NzQxLWI5OTk0NDgxYQ","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:02:27:Reminder email Note added to project and lead sent data:{"lead_id":"3078","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #6562","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:02:27:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"3078","note_id":215879}-----------------------------
2025-07-10 14:02:27: Reminder triggered for invoice no:5065
------------------------------
2025-07-10 14:02:27:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>",
						"bcc":"<EMAIL>",
						"data": {
							"customer_name":"Eric S",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=c62c1d78c44f9d1b93abf2ee4edd1da7",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-f321244b6116445c9ddfae03643ef38d3560b702d2e843649ce7cf33f0c10dc7a75b3d5c35014a0c8248c8129cf43271?locale=en_US&cta=v3invoicelink",
							"business_name":"The Sloan Firm PLLC",
							"invoice_id":"6526",
							"invoice_number":"6526",
							"invoice_date":"12/23/2024",
							"invoice_due_date":"12/23/2024",
							"overdue_days":"199",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"649.83",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:02:28:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"Njk5ZTBlNmUtNWQ2OC0xMWYwLWJkYzktNmU5MjdlMzA4YmJhLTY3OWZjY2EyMw","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:02:29:Reminder email Note added to project and lead sent data:{"lead_id":"1947","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #6526","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:02:29:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"1947","note_id":215880}-----------------------------
2025-07-10 14:02:29: Reminder triggered for invoice no:5062
------------------------------
2025-07-10 14:02:29:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>,<EMAIL>,<EMAIL>",
						"bcc":"<EMAIL>",
						"data": {
							"customer_name":"Saurin Patel",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=abb528270bc07f0756d92049ca39ae12",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-efd9f0f4c1454ab78c5a80f339f9f612de9756e73969458799ab41a8c2a11a1916632ac3e19548ebaba41b33de3d98fd?locale=en_US&cta=v3invoicelink",
							"business_name":"CHINTAMANI CORPORATION",
							"invoice_id":"6523",
							"invoice_number":"6523",
							"invoice_date":"12/23/2024",
							"invoice_due_date":"12/23/2024",
							"overdue_days":"199",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"1239.60",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:02:30:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"NmFkMTllZmQtNWQ2OC0xMWYwLWE1NDMtZGE5M2I3YjU2YWYwLTY0NWU4MzJmNg","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:02:31:Reminder email Note added to project and lead sent data:{"lead_id":"4084","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #6523","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:02:31:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"4084","note_id":215881}------------------ AT
2025-07-10 14:02:31: Reminder is terminated the loop for Test lead invoice Lead ids 9509and invoice id is5051------------------ AT
2025-07-10 14:02:31: Reminder is terminated the loop for Test lead invoice Lead ids 9509and invoice id is5050-----------------------------
2025-07-10 14:02:31: Reminder triggered for invoice no:5007
------------------------------
2025-07-10 14:02:31:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"<EMAIL>",
						"bcc":"<EMAIL>",
						"data": {
							"customer_name":"Abdul Isu",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=bfafd976aa8e6e4404af581d7661dd7a",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-891b6f2f79d544d38be96f35e73e2c8da1f0ebc3641a47adba7d8048c7a7d048a1a8e4f66b9a4b1f95bffaafcfb44f28?locale=en_US&cta=v3invoicelink",
							"business_name":"Restlives LLC",
							"invoice_id":"6468",
							"invoice_number":"6468",
							"invoice_date":"11/21/2024",
							"invoice_due_date":"11/21/2024",
							"overdue_days":"231",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"1448.77",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:02:32:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"NmMwMGZkNGMtNWQ2OC0xMWYwLWEzMTAtNGFlYjI0NjRkMWMxLWE5MzAyODA2OA","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:02:33:Reminder email Note added to project and lead sent data:{"lead_id":"2031","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #6468","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:02:33:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"2031","note_id":215883}-----------------------------
2025-07-10 14:02:33: Reminder triggered for invoice no:4945
------------------------------
2025-07-10 14:02:33:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"",
						"bcc":"<EMAIL>",
						"data": {
							"customer_name":"Kevin Longhacker",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=44ef1abc62facf85b8a853f92abed1d9",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-4b0c837593404ab5b4e58a1d4c68cae07142b9490d744e8c985aac796037401a362d1dad6fec4383b02f9bab28ebff02?locale=en_US&cta=v3invoicelink",
							"business_name":"EVG VETERINARIANS OF HARBOR POINT PLLC",
							"invoice_id":"6406",
							"invoice_number":"6406",
							"invoice_date":"11/11/2024",
							"invoice_due_date":"11/11/2024",
							"overdue_days":"241",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"5706.40",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:02:34:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"NmQzOWRmZjgtNWQ2OC0xMWYwLThhYmUtNzY1MmUxMDUwZGMyLTlhZTM2ODdlNQ","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:02:35:Reminder email Note added to project and lead sent data:{"lead_id":"6493","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #6406","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:02:35:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"6493","note_id":215884}-----------------------------
2025-07-10 14:02:35: Reminder triggered for invoice no:4943
------------------------------
2025-07-10 14:02:35:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"",
						"bcc":"<EMAIL>",
						"data": {
							"customer_name":"Kevin Longhacker",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=9094c4c479bd6184700f894a2a2b7fe8",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-330af997db4741e4a750852fde8e76a37a010f07145e470f8ece5df82510321bb0f747b79e6e44dbbfe23422a3957c5f?locale=en_US&cta=v3invoicelink",
							"business_name":"EVG VETERINARIANS OF NORTH CAROLINA",
							"invoice_id":"6404",
							"invoice_number":"6404",
							"invoice_date":"11/11/2024",
							"invoice_due_date":"11/11/2024",
							"overdue_days":"241",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"2895.67",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:02:36:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"NmU1YjUyMDItNWQ2OC0xMWYwLThkYjYtYWE2NmRjMjE2NzcxLWVmY2UzNGFkNQ","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:02:37:Reminder email Note added to project and lead sent data:{"lead_id":"6496","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #6404","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:02:37:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"6496","note_id":215885}-----------------------------
2025-07-10 14:02:37: Reminder triggered for invoice no:4942
------------------------------
2025-07-10 14:02:37:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"",
						"bcc":"<EMAIL>",
						"data": {
							"customer_name":"Eric S",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=830077c58a78b91459750ff234bd081d",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-61161f9b9530486d865d681e0c95c4710927c13e4a9d49e4b97f8ca02e022fce5c833e2a538a4b1fae13f8a6aa5fabd1?locale=en_US&cta=v3invoicelink",
							"business_name":"The Sloan Firm PLLC",
							"invoice_id":"6403",
							"invoice_number":"6403",
							"invoice_date":"11/11/2024",
							"invoice_due_date":"11/11/2024",
							"overdue_days":"241",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"368.61",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:02:38:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"NmY5MmRlYjMtNWQ2OC0xMWYwLWI1ODMtYWFkOWFkZGQzNDUzLTllOTg3YmQ4OQ","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:02:39:Reminder email Note added to project and lead sent data:{"lead_id":"1947","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #6403","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:02:39:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"1947","note_id":215886}-----------------------------
2025-07-10 14:02:39: Reminder triggered for invoice no:4924
------------------------------
2025-07-10 14:02:39:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"",
						"bcc":"<EMAIL>",
						"data": {
							"customer_name":"Shadi Sayes",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=f4278736e102fb0ca9d67eebda69ca17",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-3d99b72983094138add0d4ceffc30e1a469b2bdfbe6545878fec227470ccf132481c0babe6d04fef831d1a4d5f453371?locale=en_US&cta=v3invoicelink",
							"business_name":"IMAGE ONE ENTERPRISES LLC",
							"invoice_id":"6385",
							"invoice_number":"6385",
							"invoice_date":"11/11/2024",
							"invoice_due_date":"11/11/2024",
							"overdue_days":"241",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"2029.10",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:02:40:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"NzBjNDI3YzctNWQ2OC0xMWYwLWE5NTYtZmE2YzljM2EzZmQ2LTgzOTdiYzgyMw","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:02:41:Reminder email Note added to project and lead sent data:{"lead_id":"1364","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #6385","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:02:41:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"1364","note_id":215887}-----------------------------
2025-07-10 14:02:41: Reminder triggered for invoice no:4913
------------------------------
2025-07-10 14:02:41:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"",
						"bcc":"<EMAIL>",
						"data": {
							"customer_name":"Kevin Longacker",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=69f6495bc42dea6747ca12863f1d33fa",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-e17449cf16be42e893c2839d60a2337f06494fe734ed49138a6266de1465443bc5058bb19b1a489f86ad489c48cd737a?locale=en_US&cta=v3invoicelink",
							"business_name":"EVG VETERINARIANS OF VERMONT",
							"invoice_id":"6374",
							"invoice_number":"6374",
							"invoice_date":"11/11/2024",
							"invoice_due_date":"11/11/2024",
							"overdue_days":"241",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"5391.58",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:02:42:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"NzFmNjk1ZmQtNWQ2OC0xMWYwLTkwMDItM2UwMzkxMTFlMDg4LTU1MWVhYzQ2Yw","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:02:43:Reminder email Note added to project and lead sent data:{"lead_id":"6502","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #6374","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:02:43:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"6502","note_id":215889}-----------------------------
2025-07-10 14:02:43: Reminder triggered for invoice no:4790
------------------------------
2025-07-10 14:02:43:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"",
						"bcc":"<EMAIL>",
						"data": {
							"customer_name":"Eric S",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=67a39de783e001d8787b2b9970196704",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-1bd4c2bdc51442eca88095ad698579aff63cde542ee64f1fb7d973c39362162de76b4460a0e642589f23c03960ca8607?locale=en_US&cta=v3invoicelink",
							"business_name":"The Sloan Firm PLLC",
							"invoice_id":"6251",
							"invoice_number":"6251",
							"invoice_date":"10/28/2024",
							"invoice_due_date":"10/28/2024",
							"overdue_days":"255",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"99.04",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:02:44:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"NzMyNDEzZjAtNWQ2OC0xMWYwLWE2M2QtMmUyYTg4OWUzZmEzLTFiZDAyY2U1NA","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:02:45:Reminder email Note added to project and lead sent data:{"lead_id":"1947","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #6251","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:02:45:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"1947","note_id":215890}-----------------------------
2025-07-10 14:02:45: Reminder triggered for invoice no:4775
------------------------------
2025-07-10 14:02:45:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"",
						"bcc":"<EMAIL>",
						"data": {
							"customer_name":"Francis Pook",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=b423d0a603cf4d0bbc5f1ad18dc7157f",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-b2289b43f5bd46009668299493da3291b26a29a0ebd54f8082f346319d74b8bb303d7c0635354a5ebcb632f0e8178c5e?locale=en_US&cta=v3invoicelink",
							"business_name":"Project Painting LLC",
							"invoice_id":"6236",
							"invoice_number":"6236",
							"invoice_date":"10/28/2024",
							"invoice_due_date":"10/28/2024",
							"overdue_days":"255",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"2832.46",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:02:46:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"NzQ0OTQyMWUtNWQ2OC0xMWYwLWJkN2QtM2FhMDM1ZDU4ZDQyLTRlNWU3YjhjYQ","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:02:47:Reminder email Note added to project and lead sent data:{"lead_id":"1846","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #6236","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:02:47:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"1846","note_id":215891}-----------------------------
2025-07-10 14:02:47: Reminder triggered for invoice no:4753
------------------------------
2025-07-10 14:02:47:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"",
						"bcc":"<EMAIL>",
						"data": {
							"customer_name":"Nicholas Dugger",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=4af04e4b2149d9f8a19509f407ee3664",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-1b19cdf682fb4f49a2e08273210bd2616b212958f2f144b996d15c207b77c81b3504dc2273184b139353f7da3804140d?locale=en_US&cta=v3invoicelink",
							"business_name":"Double-N-Transport LLC",
							"invoice_id":"6214",
							"invoice_number":"6214",
							"invoice_date":"10/14/2024",
							"invoice_due_date":"10/14/2024",
							"overdue_days":"269",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"106.32",
							"invoice_type":"custom_invoice",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:02:48:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"NzU2ZTA0NjEtNWQ2OC0xMWYwLTkyOWUtOTI4OGM3N2YxYjIyLThkNzAyNzk1MQ","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:02:48:Reminder email Note added to project and lead sent data:{"lead_id":"1932","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #6214","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:02:48:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"1932","note_id":215892}-----------------------------
2025-07-10 14:02:49: Reminder triggered for invoice no:4071
------------------------------
2025-07-10 14:02:49:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"",
						"bcc":"",
						"data": {
							"customer_name":"Brian Reale",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=",
							"pay_invoice_url":"",
							"business_name":"BAT INC DBA Coast Processing",
							"invoice_id":"5532",
							"invoice_number":"5532",
							"invoice_date":"01/02/2024",
							"invoice_due_date":"01/02/2024",
							"overdue_days":"555",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"12713.25",
							"invoice_type":"success_fee",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:02:50:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"NzY5MTRjZjgtNWQ2OC0xMWYwLWEzOGUtNTZiOTViZmQ4ZmY5LTljYWNmNGY0Zg","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:02:50:Reminder email Note added to project and lead sent data:{"lead_id":"575","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #5532","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:02:50:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"575","note_id":215893}-----------------------------
2025-07-10 14:02:51: Reminder triggered for invoice no:3926
------------------------------
2025-07-10 14:02:51:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"",
						"bcc":"",
						"data": {
							"customer_name":"Terry Guzak",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=",
							"pay_invoice_url":"",
							"business_name":"Palm Springs Dentistry Inc - CCB",
							"invoice_id":"5387",
							"invoice_number":"5387",
							"invoice_date":"12/06/2022",
							"invoice_due_date":"12/16/2022",
							"overdue_days":"937",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"7808.48",
							"invoice_type":"success_fee",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:02:52:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"NzdhYWNlZGMtNWQ2OC0xMWYwLThlOWQtZmE2YzljM2EzZmQ2LWZmYjFiM2FjYw","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:02:52:Reminder email Note added to project and lead sent data:{"lead_id":"1549","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #5387","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:02:52:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"1549","note_id":215895}-----------------------------
2025-07-10 14:02:52: Reminder triggered for invoice no:3924
------------------------------
2025-07-10 14:02:52:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"",
						"bcc":"",
						"data": {
							"customer_name":"Kyle Wilkinson",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=",
							"pay_invoice_url":"",
							"business_name":"AzTech C&amp;M LLC - CCB",
							"invoice_id":"5385",
							"invoice_number":"5385",
							"invoice_date":"01/25/2023",
							"invoice_due_date":"2/4/2023",
							"overdue_days":"887",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"6125.34",
							"invoice_type":"success_fee",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:02:53:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"NzhkMDhjY2MtNWQ2OC0xMWYwLWFlZjQtYzYzNTE3ODY2NTQ5LTAyOGNmMjNiNg","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:02:54:Reminder email Note added to project and lead sent data:{"lead_id":"1397","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #5385","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:02:54:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"1397","note_id":215896}-----------------------------
2025-07-10 14:02:54: Reminder triggered for invoice no:3918
------------------------------
2025-07-10 14:02:54:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"",
						"bcc":"",
						"data": {
							"customer_name":"Daryl Purfeerst",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-d13bc69190504f2eac6d299d9dbb00c0ff14c37e648b4e36a486dca65b2be3e7b8f56cb0648e4eadb99dbe8b789c7c92?locale=en_US&cta=v3invoicelink",
							"business_name":"Northshore Paving, INC",
							"invoice_id":"5379",
							"invoice_number":"5379",
							"invoice_date":"02/28/2024",
							"invoice_due_date":"2/29/2024",
							"overdue_days":"497",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"2977.74",
							"invoice_type":"retainer",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:02:55:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"NzllYWNmYzAtNWQ2OC0xMWYwLWFiMzktYzYzNTE3ODY2NTQ5LTNjZWM5NTYxMw","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:02:56:Reminder email Note added to project and lead sent data:{"lead_id":"7299","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #5379","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:02:56:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"7299","note_id":215897}-----------------------------
2025-07-10 14:02:56: Reminder triggered for invoice no:3915
------------------------------
2025-07-10 14:02:56:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"",
						"bcc":"",
						"data": {
							"customer_name":"Blanca Yepez",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=",
							"pay_invoice_url":"",
							"business_name":"OC Smart L&L LLC - CCB",
							"invoice_id":"5376",
							"invoice_number":"5376",
							"invoice_date":"01/25/2023",
							"invoice_due_date":"2/4/2023",
							"overdue_days":"887",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"2111.06",
							"invoice_type":"success_fee",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
-------------------------------
2025-07-10 14:02:57:Reminder email api response data :{"headers":{},"original":{"status":202,"message":"Email processing completed.","batch_id":"N2IxMGMzNDItNWQ2OC0xMWYwLWE0YTItNmFlNGI1NzMxODI2LTFjYjQ0MzhlNQ","scheduled":true},"exception":null}
----------------------------------------------------------------------------------
2025-07-10 14:02:58:Reminder email Note added to project and lead sent data:{"lead_id":"584","product_id":"935","note":"An invoice reminder was sent using the Invoice Reminder: From Day 30 and onwards template for Invoice Id #5376","user_id":""}----------------------------------------------------------------------------------
2025-07-10 14:02:58:Reminder email Note added to project and lead API response :{"status":200,"message":"The note has been added successfully","lead_id":"584","note_id":215898}------------------ AT
2025-07-10 14:02:58: Reminder not send for payment plan lead, terminated the loop for invoice no:3412 and lead id:2173-----------------------------
2025-07-10 14:02:58: Reminder triggered for invoice no:3063
------------------------------
2025-07-10 14:02:58:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"",
						"bcc":"",
						"data": {
							"customer_name":"Dallas Schleining",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-62393b4e937c4857a082da824f3cf3ce887d120da44443538025ca530a39c9c854da29fb069f4cedbce2cd94f9ce8ab0?locale=en_US&cta=v3invoicelink",
							"business_name":"Schleining Genetics LLC",
							"invoice_id":"4524",
							"invoice_number":"4524",
							"invoice_date":"08/11/2023",
							"invoice_due_date":"08/21/2023",
							"overdue_days":"689",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"12713.80",
							"invoice_type":"success_fee",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}

2025-07-10 14:03:00:Reminder email api sent data :{
					"template_id": "d-4a5aed414bc6407983465372c90d3e73",
					"schedule_time":"28800",
					"recipients": [
						{
						"email": "<EMAIL>",
						"cc":"",
						"bcc":"",
						"data": {
							"customer_name":"Timothy Gaines",
							"view_url":"https://portal.occamsadvisory.com/portal/invoice-view/?token=",
							"pay_invoice_url":"https://connect.intuit.com/portal/app/CommerceNetwork/view/scs-v1-927c1988e84c46e38e627e7486f714c34d9b97a9a03d4211a0cd6913de467e13f29a830aed544ba39c5cf732527dcf60?locale=en_US&cta=v3invoicelink",
							"business_name":"GAINES NATIONAL CARRIERS, LLC",
							"invoice_id":"3956",
							"invoice_number":"3956",
							"invoice_date":"06/20/2023",
							"invoice_due_date":"06/30/2023",
							"overdue_days":"741",
							"current_date":"07/10/2025",
							"next_days":"07/11/2025",
							"invoice_amount":"2310.75",
							"invoice_type":"success_fee",
							"date_of_reminder":"07/10/2025",
							"service":"ERC"
						}
						}
					]
				}
